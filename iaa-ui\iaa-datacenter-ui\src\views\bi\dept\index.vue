<template>
  <div class="dept-bi-dashboard">
    <div class="dashboard-header">
      <h1>部门关键指标看板</h1>
      <div class="filter-section">
        <el-select v-model="selectedDept" placeholder="选择部门" @change="loadDeptData">
          <el-option v-for="dept in deptList" :key="dept.id" :label="dept.name" :value="dept.id" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="loadDeptData"
        />
      </div>
    </div>

    <div class="dashboard-content">
      <!-- 关键指标卡片 -->
      <div class="kpi-cards">
        <div v-for="(kpi, index) in kpiData" :key="index" class="kpi-card">
          <div class="kpi-title">{{ kpi.title }}</div>
          <div class="kpi-value">{{ kpi.value }}</div>
          <div class="kpi-trend" :class="kpi.trend > 0 ? 'up' : 'down'">
            {{ kpi.trend > 0 ? '↑' : '↓' }} {{ Math.abs(kpi.trend) }}%
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <div class="chart-item">
          <h3>业绩趋势</h3>
          <div ref="performanceChart" class="chart"></div>
        </div>
        <div class="chart-item">
          <h3>人员分布</h3>
          <div ref="staffChart" class="chart"></div>
        </div>
        <div class="chart-item">
          <h3>项目完成率</h3>
          <div ref="projectChart" class="chart"></div>
        </div>
        <div class="chart-item">
          <h3>预算使用情况</h3>
          <div ref="budgetChart" class="chart"></div>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="data-table">
        <h3>详细数据</h3>
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="date" label="日期" width="180" />
          <el-table-column prop="project" label="项目" width="180" />
          <el-table-column prop="progress" label="进度" />
          <el-table-column prop="budget" label="预算使用" />
          <el-table-column prop="staff" label="人员配置" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// 部门列表
const deptList = ref([
  { id: 1, name: '研发部' },
  { id: 2, name: '市场部' },
  { id: 3, name: '销售部' },
  { id: 4, name: '财务部' }
])

// 选中的部门和日期范围
const selectedDept = ref(1)
const dateRange = ref([new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()])

// KPI数据
const kpiData = ref([
  { title: '总收入', value: '¥1,234,567', trend: 12.5 },
  { title: '项目数', value: '32', trend: 8.3 },
  { title: '人员效率', value: '94%', trend: -2.1 },
  { title: '预算执行率', value: '87%', trend: 5.6 }
])

// 表格数据
const tableData = ref([])

// 图表引用
const performanceChart = ref(null)
const staffChart = ref(null)
const projectChart = ref(null)
const budgetChart = ref(null)

// 图表实例
let charts = {}

// 加载部门数据
const loadDeptData = async () => {
  // 这里应该是从API获取数据
  // 模拟API请求
  await new Promise((resolve) => setTimeout(resolve, 500))

  // 模拟数据
  tableData.value = [
    { date: '2023-05-01', project: '项目A', progress: '90%', budget: '¥50,000', staff: '5人' },
    { date: '2023-05-02', project: '项目B', progress: '75%', budget: '¥30,000', staff: '3人' },
    { date: '2023-05-03', project: '项目C', progress: '60%', budget: '¥80,000', staff: '8人' },
    { date: '2023-05-04', project: '项目D', progress: '40%', budget: '¥20,000', staff: '2人' },
    { date: '2023-05-05', project: '项目E', progress: '10%', budget: '¥10,000', staff: '1人' }
  ]

  // 更新图表
  initCharts()
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 业绩趋势图
    if (performanceChart.value) {
      charts.performance = echarts.init(performanceChart.value)
      charts.performance.setOption({
        title: { text: '' },
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月'] },
        yAxis: { type: 'value' },
        series: [{ data: [150, 230, 224, 218, 135, 147], type: 'line' }]
      })
    }

    // 人员分布图
    if (staffChart.value) {
      charts.staff = echarts.init(staffChart.value)
      charts.staff.setOption({
        tooltip: { trigger: 'item' },
        series: [
          {
            type: 'pie',
            radius: '60%',
            data: [
              { value: 5, name: '研发' },
              { value: 3, name: '设计' },
              { value: 2, name: '测试' },
              { value: 1, name: '产品' }
            ]
          }
        ]
      })
    }

    // 项目完成率图
    if (projectChart.value) {
      charts.project = echarts.init(projectChart.value)
      charts.project.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['项目A', '项目B', '项目C', '项目D', '项目E'] },
        yAxis: { type: 'value', max: 100 },
        series: [
          {
            data: [90, 75, 60, 40, 10],
            type: 'bar',
            label: { show: true, position: 'top', formatter: '{c}%' }
          }
        ]
      })
    }

    // 预算使用情况图
    if (budgetChart.value) {
      charts.budget = echarts.init(budgetChart.value)
      charts.budget.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['项目A', '项目B', '项目C', '项目D', '项目E'] },
        yAxis: { type: 'value' },
        series: [
          { name: '预算', type: 'bar', data: [50000, 30000, 80000, 20000, 10000] },
          { name: '实际', type: 'bar', data: [45000, 28000, 70000, 15000, 5000] }
        ]
      })
    }
  })
}

// 窗口大小变化时重绘图表
const handleResize = () => {
  Object.values(charts).forEach((chart) => chart && chart.resize())
}

onMounted(() => {
  loadDeptData()
  window.addEventListener('resize', handleResize)
})
</script>

<style scoped>
.dept-bi-dashboard {
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-section {
  display: flex;
  gap: 10px;
}

.kpi-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.kpi-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.kpi-title {
  font-size: 14px;
  color: #666;
}

.kpi-value {
  font-size: 24px;
  font-weight: bold;
  margin: 10px 0;
}

.kpi-trend {
  font-size: 14px;
}

.kpi-trend.up {
  color: #67c23a;
}

.kpi-trend.down {
  color: #f56c6c;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.chart-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart {
  height: 300px;
}

.data-table {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
</style>
